package link.thingscloud.mobile.area.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class PhoneDatReaderBinarySearch {
    static class Index {
        int phonePrefix;
        int recordOffset;
        byte cardType;
    }

    private final byte[] records;
    private final Index[] indexes;

    public PhoneDatReaderBinarySearch(File file) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            // 读取头部信息
            int version = readLittleEndianInt(raf);
            int indexOffset = readLittleEndianInt(raf);
            System.out.println("Binary Search Version - Version: " + version + ", Index offset: " + indexOffset);

            // 读取记录区数据
            int recordsSize = indexOffset - 8;
            records = new byte[recordsSize];
            raf.seek(8);
            raf.readFully(records);

            // 读取索引区
            raf.seek(indexOffset);
            long remainingBytes = raf.length() - indexOffset;
            int indexCount = (int) (remainingBytes / 9);
            indexes = new Index[indexCount];

            for (int i = 0; i < indexCount; i++) {
                int phonePrefix = readLittleEndianInt(raf);
                int recordOffset = readLittleEndianInt(raf);
                byte cardType = raf.readByte();

                Index idx = new Index();
                idx.phonePrefix = phonePrefix;
                idx.recordOffset = recordOffset;
                idx.cardType = cardType;
                indexes[i] = idx;
            }

            System.out.println("Binary Search Version - 总共读取了 " + indexes.length + " 个索引项");
        }
    }

    private static int readLittleEndianInt(RandomAccessFile raf) throws IOException {
        byte[] buf = new byte[4];
        raf.readFully(buf);
        return ((buf[3] & 0xFF) << 24) |
                ((buf[2] & 0xFF) << 16) |
                ((buf[1] & 0xFF) << 8) |
                (buf[0] & 0xFF);
    }

    private static String getCardTypeName(byte cardType) {
        switch (cardType) {
            case 1: return "中国移动";
            case 2: return "中国联通";
            case 3: return "中国电信";
            case 4: return "中国电信虚拟运营商";
            case 5: return "中国联通虚拟运营商";
            case 6: return "中国移动虚拟运营商";
            case 7: return "中国广电";
            case 8: return "中国广电虚拟运营商";
            default: return "未知运营商";
        }
    }

    private Map<String, String> parseRecordToMap(int offset, byte cardType) {
        try {
            int actualOffset = offset - 8;
            if (actualOffset < 0 || actualOffset >= records.length) {
                return null;
            }

            int endPos = actualOffset;
            while (endPos < records.length && records[endPos] != 0) {
                endPos++;
            }

            if (endPos >= records.length) {
                return null;
            }

            String recordStr = new String(records, actualOffset, endPos - actualOffset, StandardCharsets.UTF_8);
            String[] parts = recordStr.split("\\|");

            if (parts.length >= 4) {
                Map<String, String> recordMap = new HashMap<>();
                recordMap.put("province", parts[0]);
                recordMap.put("city", parts[1]);
                recordMap.put("zip_code", parts[2]);
                recordMap.put("area_code", parts[3]);
                recordMap.put("card_type", getCardTypeName(cardType));
                return recordMap;
            }
        } catch (Exception e) {
            System.err.println("解析记录时出错，偏移量: " + offset + ", 错误: " + e.getMessage());
        }
        return null;
    }

    // 使用二分查找
    public Map<String, String> query(String phoneNumber) {
        if (phoneNumber.length() < 7) return null;

        try {
            int phonePrefix = Integer.parseInt(phoneNumber.substring(0, 7));

            int left = 0;
            int right = indexes.length - 1;

            while (left <= right) {
                int mid = (left + right) / 2;
                int midPrefix = indexes[mid].phonePrefix;

                if (midPrefix == phonePrefix) {
                    return parseRecordToMap(indexes[mid].recordOffset, indexes[mid].cardType);
                } else if (midPrefix < phonePrefix) {
                    left = mid + 1;
                } else {
                    right = mid - 1;
                }
            }
        } catch (NumberFormatException e) {
            System.err.println("手机号前缀格式错误: " + phoneNumber.substring(0, 7));
        }

        return null;
    }
}
