package link.thingscloud.mobile.area.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class PhoneDatReader {
    static class Record {
        String province;
        String city;
        String zip_code;
        String area_code;
        String card_type;

        @Override
        public String toString() {
            return "{"
                + "\"province\": \"" + province + "\", "
                + "\"city\": \"" + city + "\", "
                + "\"zip_code\": \"" + zip_code + "\", "
                + "\"area_code\": \"" + area_code + "\", "
                + "\"card_type\": \"" + card_type + "\""
                + "}";
        }

        public Map<String, String> toMap() {
            Map<String, String> map = new HashMap<>();
            map.put("province", province);
            map.put("city", city);
            map.put("zip_code", zip_code);
            map.put("area_code", area_code);
            map.put("card_type", card_type);
            return map;
        }
    }

    static class Index {
        int phonePrefix; // 前7位，存储为int
        int recordOffset;
        byte cardType;
    }

    private final byte[] records;
    private final Index[] indexes;

    public PhoneDatReader(File file) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            // 读取头部信息
            int version = readLittleEndianInt(raf);
            int indexOffset = readLittleEndianInt(raf);
            System.out.println("Version: " + version + ", Index offset: " + indexOffset);

            // 读取记录区数据
            int recordsSize = indexOffset - 8;
            records = new byte[recordsSize];
            raf.seek(8);
            raf.readFully(records);
            System.out.println("读取记录区完成，大小: " + recordsSize + " 字节");

            // 读取索引区
            raf.seek(indexOffset);
            long remainingBytes = raf.length() - indexOffset;
            int indexCount = (int) (remainingBytes / 9); // 每个索引9字节
            indexes = new Index[indexCount];

            System.out.println("开始读取索引区，预计索引数量: " + indexCount);

            for (int i = 0; i < indexCount; i++) {
                int phonePrefix = readLittleEndianInt(raf);
                int recordOffset = readLittleEndianInt(raf);
                byte cardType = raf.readByte();

                Index idx = new Index();
                idx.phonePrefix = phonePrefix;
                idx.recordOffset = recordOffset;
                idx.cardType = cardType;
                indexes[i] = idx;

                // 只打印前几个索引项用于调试
                if (i < 5) {
                    System.out.println("索引 " + (i+1) + ": 前缀=" + phonePrefix + ", 偏移=" + recordOffset + ", 卡类型=" + cardType);
                }
            }

            System.out.println("总共读取了 " + indexCount + " 个索引项");
        }
    }

    // 小端读取4字节int
    private static int readLittleEndianInt(RandomAccessFile raf) throws IOException {
        byte[] buf = new byte[4];
        raf.readFully(buf);
        return ((buf[3] & 0xFF) << 24) |
                ((buf[2] & 0xFF) << 16) |
                ((buf[1] & 0xFF) << 8) |
                (buf[0] & 0xFF);
    }

    // 小端读取2字节无符号short
    private static int readLittleEndianUnsignedShort(RandomAccessFile raf) throws IOException {
        byte[] buf = new byte[2];
        raf.readFully(buf);
        return ((buf[1] & 0xFF) << 8) |
                (buf[0] & 0xFF);
    }

    // 根据卡类型获取运营商名称
    private static String getCardTypeName(byte cardType) {
        switch (cardType) {
            case 1: return "中国移动";
            case 2: return "中国联通";
            case 3: return "中国电信";
            case 4: return "中国电信虚拟运营商";
            case 5: return "中国联通虚拟运营商";
            case 6: return "中国移动虚拟运营商";
            case 7: return "中国广电";
            case 8: return "中国广电虚拟运营商";
            default: return "未知运营商";
        }
    }

    // 从记录区解析记录
    private Record parseRecord(int offset, byte cardType) {
        try {
            // 偏移量需要减去8，因为记录区从文件的第8字节开始
            int actualOffset = offset - 8;
            if (actualOffset < 0 || actualOffset >= records.length) {
                return null;
            }

            // 找到以\0结尾的记录
            int endPos = actualOffset;
            while (endPos < records.length && records[endPos] != 0) {
                endPos++;
            }

            if (endPos >= records.length) {
                return null;
            }

            // 提取记录字符串
            String recordStr = new String(records, actualOffset, endPos - actualOffset, StandardCharsets.UTF_8);
            String[] parts = recordStr.split("\\|");

            if (parts.length >= 4) {
                Record rec = new Record();
                rec.province = parts[0];
                rec.city = parts[1];
                rec.zip_code = parts[2];
                rec.area_code = parts[3];
                rec.card_type = getCardTypeName(cardType);
                return rec;
            }
        } catch (Exception e) {
            System.err.println("解析记录时出错，偏移量: " + offset + ", 错误: " + e.getMessage());
        }
        return null;
    }

    // 使用二分查找查询手机号
    public Record query(String phoneNumber) {
        if (phoneNumber.length() < 7) return null;

        try {
            int phonePrefix = Integer.parseInt(phoneNumber.substring(0, 7));

            int left = 0;
            int right = indexes.length - 1;

            while (left <= right) {
                int mid = (left + right) / 2;
                int midPrefix = indexes[mid].phonePrefix;

                if (midPrefix == phonePrefix) {
                    // 找到匹配的前缀，解析对应的记录
                    return parseRecord(indexes[mid].recordOffset, indexes[mid].cardType);
                } else if (midPrefix < phonePrefix) {
                    left = mid + 1;
                } else {
                    right = mid - 1;
                }
            }
        } catch (NumberFormatException e) {
            System.err.println("手机号前缀格式错误: " + phoneNumber.substring(0, 7));
        }

        return null;
    }

    // 查询手机号并返回Map格式的结果
    public Map<String, String> queryAsMap(String phoneNumber) {
        Record record = query(phoneNumber);
        return record != null ? record.toMap() : null;
    }

    public static void main(String[] args) throws IOException {
        File datFile = new File("/Users/<USER>/Documents/001-workspace/02-work/01-telecom/03-code/01-cc/mobile-area-2.0-dev/src/main/resources/phone.dat");
        if (!datFile.exists()) {
            System.err.println("phone.dat 文件不存在");
            return;
        }

        PhoneDatReader reader = new PhoneDatReader(datFile);

        // 测试查询手机号前7位
        String testPhone = "1925541";
        Record result = reader.query(testPhone);
        if (result != null) {
            System.out.println("查询结果 (Record): " + result);

            // 测试Map格式输出
            Map<String, String> mapResult = reader.queryAsMap(testPhone);
            System.out.println("查询结果 (Map): " + mapResult);
        } else {
            System.out.println("未找到手机号: " + testPhone);
        }
    }
}

