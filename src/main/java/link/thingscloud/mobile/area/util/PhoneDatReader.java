package link.thingscloud.mobile.area.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class PhoneDatReader {
    static class Record {
        String province;
        String city;
        String zip;
        String areaCode;

        @Override
        public String toString() {
            return province + " " + city + " ZIP:" + zip + " AreaCode:" + areaCode;
        }
    }

    static class Index {
        String phonePrefix; // 前7位
        int recordOffset;
        byte cardType;
    }

    private final Map<Integer, Record> offsetToRecord = new HashMap<>();
    private final Map<String, Index> phoneToIndex = new HashMap<>();

    public PhoneDatReader(File file) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            int version = readLittleEndianInt(raf);
            int indexOffset = readLittleEndianInt(raf);
            System.out.println("Version: " + version + ", Index offset: " + indexOffset);

            // 读取记录区 (从8开始，到indexOffset结束)
            raf.seek(8);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int currentOffset = 8;

            while (currentOffset < indexOffset) {
                int recordStartOffset = currentOffset; // 记录当前记录的起始位置
                byte b = raf.readByte();
                currentOffset++;

                if (b != 0) {
                    baos.write(b);
                } else {
                    // 读到一条完整记录
                    String recordStr = baos.toString(StandardCharsets.UTF_8.name());
                    baos.reset();

                    String[] parts = recordStr.split("\\|");
                    if (parts.length >= 4) {
                        Record rec = new Record();
                        rec.province = parts[0];
                        rec.city = parts[1];
                        rec.zip = parts[2];
                        rec.areaCode = parts[3];

                        // 将记录存储到映射中，使用记录的起始偏移量作为键
                        offsetToRecord.put(recordStartOffset, rec);
                    }
                }
            }

            // 读取索引区
            raf.seek(indexOffset);
            System.out.println("开始读取索引区，文件长度: " + raf.length() + ", 当前位置: " + raf.getFilePointer());

            int indexCount = 0;
            while (raf.getFilePointer() < raf.length()) {
                // 检查剩余字节数是否足够读取一个完整的索引项 (7 + 2 + 1 = 10字节)
                long remainingBytes = raf.length() - raf.getFilePointer();
                if (remainingBytes < 9) {  // 修正为9字节
                    System.out.println("剩余字节数不足，停止读取索引。剩余字节: " + remainingBytes);
                    break;
                }

                byte[] prefixBytes = new byte[7];
                raf.readFully(prefixBytes);
                String phonePrefix = new String(prefixBytes, StandardCharsets.US_ASCII);

                int recordOffset = readLittleEndianUnsignedShort(raf);
                byte cardType = raf.readByte();

                Index idx = new Index();
                idx.phonePrefix = phonePrefix;
                idx.recordOffset = recordOffset;
                idx.cardType = cardType;

                phoneToIndex.put(phonePrefix, idx);
                indexCount++;

                // 只打印前几个索引项用于调试
                if (indexCount <= 5) {
                    System.out.println("索引 " + indexCount + ": 前缀=" + phonePrefix + ", 偏移=" + recordOffset + ", 卡类型=" + cardType);
                }
            }

            System.out.println("总共读取了 " + indexCount + " 个索引项");
            System.out.println("总共读取了 " + offsetToRecord.size() + " 条记录");
        }
    }

    // 小端读取4字节int
    private static int readLittleEndianInt(RandomAccessFile raf) throws IOException {
        byte[] buf = new byte[4];
        raf.readFully(buf);
        return ((buf[3] & 0xFF) << 24) |
                ((buf[2] & 0xFF) << 16) |
                ((buf[1] & 0xFF) << 8) |
                (buf[0] & 0xFF);
    }

    // 小端读取2字节无符号short
    private static int readLittleEndianUnsignedShort(RandomAccessFile raf) throws IOException {
        byte[] buf = new byte[2];
        raf.readFully(buf);
        return ((buf[1] & 0xFF) << 8) |
                (buf[0] & 0xFF);
    }

    public Record query(String phoneNumber) {
        if (phoneNumber.length() < 7) return null;
        String prefix = phoneNumber.substring(0, 7);
        Index idx = phoneToIndex.get(prefix);
        if (idx == null) return null;

        return offsetToRecord.get(idx.recordOffset);
    }

    public static void main(String[] args) throws IOException {
        File datFile = new File("/Users/<USER>/Documents/001-workspace/02-work/01-telecom/03-code/01-cc/mobile-area-2.0-dev/src/main/resources/phone.dat");
        if (!datFile.exists()) {
            System.err.println("phone.dat 文件不存在");
            return;
        }

        PhoneDatReader reader = new PhoneDatReader(datFile);

        // 测试查询手机号前7位
        String testPhone = "1895319";
        Record result = reader.query(testPhone);
        if (result != null) {
            System.out.println("查询结果: " + result);
        } else {
            System.out.println("未找到手机号: " + testPhone);
        }
    }
}

