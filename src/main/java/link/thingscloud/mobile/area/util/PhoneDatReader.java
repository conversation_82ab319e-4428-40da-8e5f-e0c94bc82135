package link.thingscloud.mobile.area.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;

public class PhoneDatReader {
    static class Record {
        String province;
        String city;
        String zip_code;
        String area_code;
        String card_type;

        @Override
        public String toString() {
            return "{"
                + "\"province\": \"" + province + "\", "
                + "\"city\": \"" + city + "\", "
                + "\"zip_code\": \"" + zip_code + "\", "
                + "\"area_code\": \"" + area_code + "\", "
                + "\"card_type\": \"" + card_type + "\""
                + "}";
        }

        public Map<String, String> toMap() {
            Map<String, String> map = new HashMap<>();
            map.put("province", province);
            map.put("city", city);
            map.put("zip_code", zip_code);
            map.put("area_code", area_code);
            map.put("card_type", card_type);
            return map;
        }
    }

    static class Index {
        int phonePrefix; // 前7位，存储为int
        int recordOffset;
        byte cardType;
    }

    private final Map<String, Map<String, String>> phoneDataMap = new HashMap<>();

    public PhoneDatReader(File file) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            // 读取头部信息
            int version = readLittleEndianInt(raf);
            int indexOffset = readLittleEndianInt(raf);
            System.out.println("Version: " + version + ", Index offset: " + indexOffset);

            // 读取记录区数据
            int recordsSize = indexOffset - 8;
            byte[] records = new byte[recordsSize];
            raf.seek(8);
            raf.readFully(records);
            System.out.println("读取记录区完成，大小: " + recordsSize + " 字节");

            // 读取索引区并构建Map
            raf.seek(indexOffset);
            long remainingBytes = raf.length() - indexOffset;
            int indexCount = (int) (remainingBytes / 9); // 每个索引9字节

            System.out.println("开始读取索引区，预计索引数量: " + indexCount);

            for (int i = 0; i < indexCount; i++) {
                int phonePrefix = readLittleEndianInt(raf);
                int recordOffset = readLittleEndianInt(raf);
                byte cardType = raf.readByte();

                // 解析记录
                Map<String, String> recordData = parseRecordToMap(records, recordOffset, cardType);
                if (recordData != null) {
                    String phonePrefixStr = String.format("%07d", phonePrefix);
                    phoneDataMap.put(phonePrefixStr, recordData);
                }

                // 只打印前几个索引项用于调试
                if (i < 5) {
                    System.out.println("索引 " + (i+1) + ": 前缀=" + phonePrefix + " (" + String.format("%07d", phonePrefix) + "), 偏移=" + recordOffset + ", 卡类型=" + cardType);
                    if (recordData != null) {
                        System.out.println("  记录数据: " + recordData);
                    }
                }
            }

            System.out.println("总共加载了 " + phoneDataMap.size() + " 条记录到Map中");
            System.out.println(phoneDataMap.get("1895319"));
            System.out.println(phoneDataMap.get("1762130"));
        }
    }

    // 小端读取4字节int
    private static int readLittleEndianInt(RandomAccessFile raf) throws IOException {
        byte[] buf = new byte[4];
        raf.readFully(buf);
        return ((buf[3] & 0xFF) << 24) |
                ((buf[2] & 0xFF) << 16) |
                ((buf[1] & 0xFF) << 8) |
                (buf[0] & 0xFF);
    }

    // 小端读取2字节无符号short
    private static int readLittleEndianUnsignedShort(RandomAccessFile raf) throws IOException {
        byte[] buf = new byte[2];
        raf.readFully(buf);
        return ((buf[1] & 0xFF) << 8) |
                (buf[0] & 0xFF);
    }

    // 根据卡类型获取运营商名称
    private static String getCardTypeName(byte cardType) {
        switch (cardType) {
            case 1: return "中国移动";
            case 2: return "中国联通";
            case 3: return "中国电信";
            case 4: return "中国电信虚拟运营商";
            case 5: return "中国联通虚拟运营商";
            case 6: return "中国移动虚拟运营商";
            case 7: return "中国广电";
            case 8: return "中国广电虚拟运营商";
            default: return "未知运营商";
        }
    }

    // 从记录区解析记录并返回Map
    private Map<String, String> parseRecordToMap(byte[] records, int offset, byte cardType) {
        try {
            // 偏移量需要减去8，因为记录区从文件的第8字节开始
            int actualOffset = offset - 8;
            if (actualOffset < 0 || actualOffset >= records.length) {
                return null;
            }

            // 找到以\0结尾的记录
            int endPos = actualOffset;
            while (endPos < records.length && records[endPos] != 0) {
                endPos++;
            }

            if (endPos >= records.length) {
                return null;
            }

            // 提取记录字符串
            String recordStr = new String(records, actualOffset, endPos - actualOffset, StandardCharsets.UTF_8);
            String[] parts = recordStr.split("\\|");

            if (parts.length >= 4) {
                Map<String, String> recordMap = new HashMap<>();
                recordMap.put("province", parts[0]);
                recordMap.put("city", parts[1]);
                recordMap.put("zip_code", parts[2]);
                recordMap.put("area_code", parts[3]);
                recordMap.put("card_type", getCardTypeName(cardType));
                return recordMap;
            }
        } catch (Exception e) {
            System.err.println("解析记录时出错，偏移量: " + offset + ", 错误: " + e.getMessage());
        }
        return null;
    }

    // 直接从Map中查询手机号信息
    public Map<String, String> query(String phoneNumber) {
        if (phoneNumber.length() < 7) {
            return null;
        }

        String phonePrefix = phoneNumber.substring(0, 7);
        return phoneDataMap.get(phonePrefix);
    }

    // 为了兼容性，保留Record格式的查询方法
    public Record queryAsRecord(String phoneNumber) {
        Map<String, String> data = query(phoneNumber);
        if (data == null) {
            return null;
        }

        Record record = new Record();
        record.province = data.get("province");
        record.city = data.get("city");
        record.zip_code = data.get("zip_code");
        record.area_code = data.get("area_code");
        record.card_type = data.get("card_type");
        return record;
    }

    // 导出所有数据到CSV文件
    public void exportToCsv(String csvFilePath) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(csvFilePath, StandardCharsets.UTF_8))) {
            // 写入CSV头部
            writer.println("前缀,省,市,运营商,area_code");

            // 获取所有前缀并排序
            List<String> sortedPrefixes = new ArrayList<>(phoneDataMap.keySet());
            Collections.sort(sortedPrefixes);

            // 写入数据行
            for (String prefix : sortedPrefixes) {
                Map<String, String> data = phoneDataMap.get(prefix);
                if (data != null) {
                    writer.printf("%s,%s,%s,%s,%s%n",
                        prefix,
                        escapeCsvField(data.get("province")),
                        escapeCsvField(data.get("city")),
                        escapeCsvField(data.get("card_type")),
                        escapeCsvField(data.get("area_code"))
                    );
                }
            }

            System.out.println("成功导出 " + sortedPrefixes.size() + " 条记录到文件: " + csvFilePath);
        }
    }

    // CSV字段转义处理
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }

    public static void main(String[] args) throws IOException {
        File datFile = new File("/Users/<USER>/Documents/001-workspace/02-work/01-telecom/03-code/01-cc/mobile-area-2.0-dev/src/main/resources/phone.dat");
        if (!datFile.exists()) {
            System.err.println("phone.dat 文件不存在");
            return;
        }

        PhoneDatReader reader = new PhoneDatReader(datFile);

        // 导出所有数据到CSV文件
        System.out.println("\n=== 导出CSV文件 ===");
        String csvPath = "phone_data_export.csv";
        reader.exportToCsv(csvPath);

        // 测试查询手机号前7位
        String testPhone = "1925541";
        Map<String, String> result = reader.query(testPhone);
        if (result != null) {
            System.out.println("查询结果 (Map): " + result);

            // 测试Record格式输出
            Record recordResult = reader.queryAsRecord(testPhone);
            System.out.println("查询结果 (Record): " + recordResult);
        } else {
            System.out.println("未找到手机号: " + testPhone);
        }

        // 测试多个手机号
        String[] testPhones = {"1300000", "1895319", "1925541"};
        System.out.println("\n批量测试:");
        for (String phone : testPhones) {
            Map<String, String> data = reader.query(phone);
            if (data != null) {
                System.out.println(phone + " -> " + data);
            } else {
                System.out.println(phone + " -> 未找到");
            }
        }

        // 性能测试
        System.out.println("\n=== 性能测试 ===");
        String[] performanceTestPhones = {"1895319", "1762130", "1380013", "1925541", "1300000"};
        int testRounds = 100000;

        long startTime = System.nanoTime();
        for (int i = 0; i < testRounds; i++) {
            for (String phone : performanceTestPhones) {
                reader.query(phone);
            }
        }
        long endTime = System.nanoTime();

        double totalTimeMs = (endTime - startTime) / 1_000_000.0;
        double avgTimePerQuery = totalTimeMs / (testRounds * performanceTestPhones.length);

        System.out.println("总测试次数: " + (testRounds * performanceTestPhones.length));
        System.out.println("总耗时: " + String.format("%.2f", totalTimeMs) + " ms");
        System.out.println("平均每次查询耗时: " + String.format("%.6f", avgTimePerQuery) + " ms");
        System.out.println("每秒可处理查询数: " + String.format("%.0f", 1000.0 / avgTimePerQuery));
    }
}

