package link.thingscloud.mobile.area.util;

import java.io.File;
import java.io.IOException;
import java.util.Map;

public class PerformanceComparison {
    public static void main(String[] args) throws IOException {
        File datFile = new File("src/main/resources/phone.dat");
        if (!datFile.exists()) {
            System.err.println("phone.dat 文件不存在");
            return;
        }

        System.out.println("=== 性能对比测试 ===\n");

        // 测试初始化时间
        System.out.println("1. 初始化时间对比:");
        
        long startTime = System.nanoTime();
        PhoneDatReader hashMapReader = new PhoneDatReader(datFile);
        long hashMapInitTime = System.nanoTime() - startTime;
        
        startTime = System.nanoTime();
        PhoneDatReaderBinarySearch binarySearchReader = new PhoneDatReaderBinarySearch(datFile);
        long binarySearchInitTime = System.nanoTime() - startTime;
        
        System.out.println("HashMap方式初始化时间: " + String.format("%.2f", hashMapInitTime / 1_000_000.0) + " ms");
        System.out.println("二分查找方式初始化时间: " + String.format("%.2f", binarySearchInitTime / 1_000_000.0) + " ms");
        System.out.println("HashMap初始化时间是二分查找的 " + String.format("%.1f", (double)hashMapInitTime / binarySearchInitTime) + " 倍\n");

        // 测试查询性能
        System.out.println("2. 查询性能对比:");
        String[] testPhones = {"1895319", "1762130", "1380013", "1925541", "1300000"};
        int testRounds = 100000;

        // HashMap查询测试
        startTime = System.nanoTime();
        for (int i = 0; i < testRounds; i++) {
            for (String phone : testPhones) {
                hashMapReader.query(phone);
            }
        }
        long hashMapQueryTime = System.nanoTime() - startTime;

        // 二分查找测试
        startTime = System.nanoTime();
        for (int i = 0; i < testRounds; i++) {
            for (String phone : testPhones) {
                binarySearchReader.query(phone);
            }
        }
        long binarySearchQueryTime = System.nanoTime() - startTime;

        int totalQueries = testRounds * testPhones.length;
        double hashMapAvgTime = (hashMapQueryTime / 1_000_000.0) / totalQueries;
        double binarySearchAvgTime = (binarySearchQueryTime / 1_000_000.0) / totalQueries;

        System.out.println("总查询次数: " + totalQueries);
        System.out.println("HashMap查询总时间: " + String.format("%.2f", hashMapQueryTime / 1_000_000.0) + " ms");
        System.out.println("二分查找总时间: " + String.format("%.2f", binarySearchQueryTime / 1_000_000.0) + " ms");
        System.out.println("HashMap平均每次查询: " + String.format("%.6f", hashMapAvgTime) + " ms");
        System.out.println("二分查找平均每次查询: " + String.format("%.6f", binarySearchAvgTime) + " ms");
        System.out.println("HashMap查询速度是二分查找的 " + String.format("%.1f", binarySearchAvgTime / hashMapAvgTime) + " 倍\n");

        // 内存使用估算
        System.out.println("3. 内存使用估算:");
        Runtime runtime = Runtime.getRuntime();
        long usedMemoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        // 强制垃圾回收
        System.gc();
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long usedMemoryAfter = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = usedMemoryAfter - usedMemoryBefore;
        
        System.out.println("当前内存使用: " + String.format("%.2f", memoryUsed / (1024.0 * 1024.0)) + " MB");
        System.out.println("注意: 精确的内存测量需要专门的工具\n");

        // 验证结果一致性
        System.out.println("4. 结果一致性验证:");
        boolean allMatch = true;
        for (String phone : testPhones) {
            Map<String, String> hashMapResult = hashMapReader.query(phone);
            Map<String, String> binarySearchResult = binarySearchReader.query(phone);
            
            boolean match = (hashMapResult == null && binarySearchResult == null) ||
                           (hashMapResult != null && hashMapResult.equals(binarySearchResult));
            
            if (!match) {
                System.out.println("结果不一致: " + phone);
                System.out.println("HashMap: " + hashMapResult);
                System.out.println("二分查找: " + binarySearchResult);
                allMatch = false;
            }
        }
        
        if (allMatch) {
            System.out.println("✓ 所有测试用例结果一致");
        }

        // 总结
        System.out.println("\n=== 总结 ===");
        System.out.println("HashMap方式:");
        System.out.println("  优点: 查询速度快 O(1)，适合频繁查询");
        System.out.println("  缺点: 初始化慢，内存占用大");
        System.out.println("\n二分查找方式:");
        System.out.println("  优点: 初始化快，内存占用小");
        System.out.println("  缺点: 查询速度相对慢 O(log n)");
        System.out.println("\n建议:");
        if (hashMapInitTime < binarySearchInitTime * 10 && hashMapAvgTime < binarySearchAvgTime) {
            System.out.println("  如果应用需要频繁查询，推荐使用HashMap方式");
        } else {
            System.out.println("  如果应用查询不频繁或内存有限，推荐使用二分查找方式");
        }
    }
}
